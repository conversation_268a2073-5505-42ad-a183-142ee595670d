import google.generativeai as genai 
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import config  # Import database and API configurations


engine = create_engine(config.DATABASE_URL)
Session = sessionmaker(bind=engine)

genai.configure(api_key=config.GEMINI_API_KEY)
model = genai.GenerativeModel("gemini-2.0-flash")


def summarize_text(text):
    if not text.strip():
        return "<p>No content available for summarization.</p>"

    prompt = f"""Summarize the following status update in exactly 5 bullet points and return the output as a proper HTML unordered list (<ul><li>...</li></ul>).  
The output should look like this:  
<ul>  
<li>Point 1</li>  
<li>Point 2</li>  
<li>Point 3</li>  
<li>Point 4</li>  
<li>Point 5</li>  
</ul>  
DO NOT ADD MARKDOWN OR CODE BLOCKS (e.g., ```html, ``` or similar). Only return the raw HTML list.  

{text}"""


    try:
        response = model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        print(f"Error in summarization: {e}")
        return "<p>Summarization failed.</p>"


def process_status_updates():
    session = Session()
    try:
        
        query = text("""
            SELECT employee_id, IDSR_date, body 
            FROM kuber.status_archives 
            WHERE IDSR_date >= "2025-01-01" and summerization_done = 0
        """)
        results = session.execute(query).fetchall()

        if not results:
            print("No new status updates found for summarization.")
            return

        
        for row in results:
            employee_id, idsr_date, body_html = row

            
            summary_html = summarize_text(body_html)

            
            insert_query = text("""
                INSERT INTO daily_summaries (user_id, ISO_Day_Number, IDSR_date, Summary, created_at)
                VALUES (:employee_id, DAYOFYEAR(:idsr_date), :idsr_date, :summary, NOW())
            """)
            session.execute(insert_query, {
                "employee_id": employee_id,
                "idsr_date": idsr_date,
                "summary": summary_html
            })

            
            update_query = text("""
                UPDATE kuber.status_archives 
                SET summerization_done = 1 
                WHERE employee_id = :employee_id AND IDSR_date = :idsr_date
            """)
            session.execute(update_query, {
                "employee_id": employee_id,
                "idsr_date": idsr_date
            })

        session.commit()
        print("Summarization completed, and status updated successfully!")

    except Exception as e:
        session.rollback()
        print(f"Error processing updates: {e}")

    finally:
        session.close()


if __name__ == "__main__":
    process_status_updates()
