import google.generativeai as genai
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import config  


engine = create_engine(config.DATABASE_URL)
Session = sessionmaker(bind=engine)


genai.configure(api_key=config.GEMINI_API_KEY)
model = genai.GenerativeModel("gemini-2.0-flash")

def summarize_text(text):
    if not text.strip():
        return "<p>No content available for summarization.</p>"

    prompt = f"""Summarize the following status update in exactly 7 bullet points and return the output as a proper HTML unordered list (<ul><li>...</li></ul>).  
The output should look like this:  
<ul>  
<li>Point 1</li>  
<li>Point 2</li>  
<li>Point 3</li>  
<li>Point 4</li>  
<li>Point 5</li>
<li>Point 6</li>
<li>Point 7</li>
</ul>  
DO NOT ADD MARKDOWN OR CODE BLOCKS (e.g., ```html, ``` or similar). Only return the raw HTML list.  

{text}"""

    try:
        response = model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        print(f"Error in summarization: {e}")
        return "<p>Summarization failed.</p>"


def process_weekly_summaries():
    session = Session()
    try:
        summary_query = text("""
            SELECT user_id, 
                   LPAD(WEEK(IDSR_date, 3), 2, '0') AS ISO_Week_Number, 
                   YEAR(IDSR_date) AS Year,
                   GROUP_CONCAT(Summary SEPARATOR ' ') AS Combined_Summary
            FROM daily_summaries
            WHERE IDSR_date >= CURDATE() - INTERVAL 7 DAY
            GROUP BY user_id, ISO_Week_Number, Year
        """)
        results = session.execute(summary_query).fetchall()

        if not results:
            print("No new weekly summaries found.")
            return

        for employee_id, iso_week, year, combined_summary in results:
            iso_week_number = f"{iso_week}_{year}"  

            weekly_summary_html = summarize_text(combined_summary)

            check_query = text("""
                SELECT COUNT(*) FROM weekly_summaries 
                WHERE user_id = :employee_id 
                AND ISO_Week_Number = :iso_week_number
            """)
            existing_count = session.execute(check_query, {
                "employee_id": employee_id,
                "iso_week_number": iso_week_number
            }).scalar()

            if existing_count == 0: 
                insert_query = text("""
                    INSERT INTO weekly_summaries (user_id, ISO_Week_Number, Summary, created_at)
                    VALUES (:employee_id, :iso_week_number, :summary, NOW())
                """)
                session.execute(insert_query, {
                    "employee_id": employee_id,
                    "iso_week_number": iso_week_number,
                    "summary": weekly_summary_html
                })

        session.commit()
        print("Weekly summarization completed successfully!")

    except Exception as e:
        session.rollback()
        print(f"Error processing weekly summaries: {e}")

    finally:
        session.close()


if __name__ == "__main__":
    process_weekly_summaries()
